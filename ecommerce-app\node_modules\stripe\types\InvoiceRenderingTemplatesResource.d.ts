// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    interface InvoiceRenderingTemplateRetrieveParams {
      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      version?: number;
    }

    interface InvoiceRenderingTemplateListParams extends PaginationParams {
      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      status?: InvoiceRenderingTemplateListParams.Status;
    }

    namespace InvoiceRenderingTemplateListParams {
      type Status = 'active' | 'archived';
    }

    interface InvoiceRenderingTemplateArchiveParams {
      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    interface InvoiceRenderingTemplateUnarchiveParams {
      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    class InvoiceRenderingTemplatesResource {
      /**
       * Retrieves an invoice rendering template with the given ID. It by default returns the latest version of the template. Optionally, specify a version to see previous versions.
       */
      retrieve(
        id: string,
        params?: InvoiceRenderingTemplateRetrieveParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.InvoiceRenderingTemplate>>;
      retrieve(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.InvoiceRenderingTemplate>>;

      /**
       * List all templates, ordered by creation date, with the most recently created template appearing first.
       */
      list(
        params?: InvoiceRenderingTemplateListParams,
        options?: RequestOptions
      ): ApiListPromise<Stripe.InvoiceRenderingTemplate>;
      list(
        options?: RequestOptions
      ): ApiListPromise<Stripe.InvoiceRenderingTemplate>;

      /**
       * Updates the status of an invoice rendering template to ‘archived' so no new Stripe objects (customers, invoices, etc.) can reference it. The template can also no longer be updated. However, if the template is already set on a Stripe object, it will continue to be applied on invoices generated by it.
       */
      archive(
        id: string,
        params?: InvoiceRenderingTemplateArchiveParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.InvoiceRenderingTemplate>>;
      archive(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.InvoiceRenderingTemplate>>;

      /**
       * Unarchive an invoice rendering template so it can be used on new Stripe objects again.
       */
      unarchive(
        id: string,
        params?: InvoiceRenderingTemplateUnarchiveParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.InvoiceRenderingTemplate>>;
      unarchive(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.InvoiceRenderingTemplate>>;
    }
  }
}
