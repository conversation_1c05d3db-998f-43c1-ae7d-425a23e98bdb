import type { TSESTree } from '@typescript-eslint/types';
import type { ScopeManager } from '../ScopeManager';
import type { Scope } from './Scope';
import { ScopeBase } from './ScopeBase';
import { ScopeType } from './ScopeType';
export declare class ModuleScope extends ScopeBase<ScopeType.module, TSESTree.Program, Scope> {
    constructor(scopeManager: ScopeManager, upperScope: ModuleScope['upper'], block: ModuleScope['block']);
}
//# sourceMappingURL=ModuleScope.d.ts.map