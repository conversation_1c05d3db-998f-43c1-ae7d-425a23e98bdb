export type ErrorMessageType = React.ReactNode;
type ErrorMessageProps = {
    errorMessage: ErrorMessageType;
};
export declare function ErrorMessage({ errorMessage }: ErrorMessageProps): import("react/jsx-runtime").JSX.Element;
export declare const styles = "\n  .nextjs__container_errors_wrapper {\n    position: relative;\n  }\n\n  .nextjs__container_errors_desc {\n    margin: 0;\n    margin-left: 4px;\n    color: var(--color-red-900);\n    font-weight: 500;\n    font-size: var(--size-16);\n    letter-spacing: -0.32px;\n    line-height: var(--size-24);\n    overflow-wrap: break-word;\n    white-space: pre-wrap;\n  }\n\n  .nextjs__container_errors_desc.truncated {\n    max-height: 200px;\n    overflow: hidden;\n  }\n\n  .nextjs__container_errors_gradient_overlay {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    height: 85px;\n    background: linear-gradient(\n      180deg,\n      rgba(250, 250, 250, 0) 0%,\n      var(--color-background-100) 100%\n    );\n  }\n\n  .nextjs__container_errors_expand_button {\n    position: absolute;\n    bottom: 10px;\n    left: 50%;\n    transform: translateX(-50%);\n    display: flex;\n    align-items: center;\n    padding: 6px 8px;\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-alpha-400);\n    border-radius: 999px;\n    box-shadow:\n      0px 2px 2px var(--color-gray-alpha-100),\n      0px 8px 8px -8px var(--color-gray-alpha-100);\n    font-size: var(--size-13);\n    cursor: pointer;\n    color: var(--color-gray-900);\n    font-weight: 500;\n    transition: background-color 0.2s ease;\n  }\n\n  .nextjs__container_errors_expand_button:hover {\n    background: var(--color-gray-100);\n  }\n";
export {};
