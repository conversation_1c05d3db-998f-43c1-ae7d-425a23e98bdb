import type { ReadyRuntimeError } from '../../../../utils/get-error-by-type';
type ErrorPaginationProps = {
    runtimeErrors: ReadyRuntimeError[];
    activeIdx: number;
    onActiveIndexChange: (index: number) => void;
};
export declare function ErrorOverlayPagination({ runtimeErrors, activeIdx, onActiveIndexChange, }: ErrorPaginationProps): import("react/jsx-runtime").JSX.Element;
export declare const styles = "\n  .error-overlay-pagination {\n    -webkit-font-smoothing: antialiased;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    gap: 8px;\n    width: fit-content;\n  }\n\n  .error-overlay-pagination-count {\n    color: var(--color-gray-900);\n    text-align: center;\n    font-size: var(--size-14);\n    font-weight: 500;\n    line-height: var(--size-16);\n    font-variant-numeric: tabular-nums;\n  }\n\n  .error-overlay-pagination-button {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    width: var(--size-24);\n    height: var(--size-24);\n    background: var(--color-gray-300);\n    flex-shrink: 0;\n\n    border: none;\n    border-radius: var(--rounded-full);\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n\n    &:focus-visible {\n      outline: var(--focus-ring);\n    }\n\n    &:not(:disabled):active {\n      background: var(--color-gray-500);\n    }\n\n    &:disabled {\n      opacity: 0.5;\n      cursor: not-allowed;\n    }\n  }\n\n  .error-overlay-pagination-button-icon {\n    color: var(--color-gray-1000);\n  }\n";
export {};
