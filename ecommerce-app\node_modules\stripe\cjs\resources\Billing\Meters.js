"use strict";
// File generated from our OpenAPI spec
Object.defineProperty(exports, "__esModule", { value: true });
exports.Meters = void 0;
const StripeResource_js_1 = require("../../StripeResource.js");
const stripeMethod = StripeResource_js_1.StripeResource.method;
exports.Meters = StripeResource_js_1.StripeResource.extend({
    create: stripeMethod({ method: 'POST', fullPath: '/v1/billing/meters' }),
    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/billing/meters/{id}' }),
    update: stripeMethod({ method: 'POST', fullPath: '/v1/billing/meters/{id}' }),
    list: stripeMethod({
        method: 'GET',
        fullPath: '/v1/billing/meters',
        methodType: 'list',
    }),
    deactivate: stripeMethod({
        method: 'POST',
        fullPath: '/v1/billing/meters/{id}/deactivate',
    }),
    listEventSummaries: stripeMethod({
        method: 'GET',
        fullPath: '/v1/billing/meters/{id}/event_summaries',
        methodType: 'list',
    }),
    reactivate: stripeMethod({
        method: 'POST',
        fullPath: '/v1/billing/meters/{id}/reactivate',
    }),
});
