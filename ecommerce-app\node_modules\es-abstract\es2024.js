'use strict';

/* eslint global-require: 0 */
// https://262.ecma-international.org/15.0/#sec-abstract-operations
var ES2024 = {
	abs: require('./2024/abs'),
	AddEntriesFromIterable: require('./2024/AddEntriesFromIterable'),
	AddToKeptObjects: require('./2024/AddToKeptObjects'),
	AddValueToKeyedGroup: require('./2024/AddValueToKeyedGroup'),
	AdvanceStringIndex: require('./2024/AdvanceStringIndex'),
	AllCharacters: require('./2024/AllCharacters'),
	ApplyStringOrNumericBinaryOperator: require('./2024/ApplyStringOrNumericBinaryOperator'),
	ArrayBufferByteLength: require('./2024/ArrayBufferByteLength'),
	ArrayBufferCopyAndDetach: require('./2024/ArrayBufferCopyAndDetach'),
	ArrayCreate: require('./2024/ArrayCreate'),
	ArraySetLength: require('./2024/ArraySetLength'),
	ArraySpeciesCreate: require('./2024/ArraySpeciesCreate'),
	AsyncFromSyncIteratorContinuation: require('./2024/AsyncFromSyncIteratorContinuation'),
	AsyncIteratorClose: require('./2024/AsyncIteratorClose'),
	BigInt: require('./2024/BigInt'),
	BigIntBitwiseOp: require('./2024/BigIntBitwiseOp'),
	BinaryAnd: require('./2024/BinaryAnd'),
	BinaryOr: require('./2024/BinaryOr'),
	BinaryXor: require('./2024/BinaryXor'),
	ByteListBitwiseOp: require('./2024/ByteListBitwiseOp'),
	ByteListEqual: require('./2024/ByteListEqual'),
	Call: require('./2024/Call'),
	CanBeHeldWeakly: require('./2024/CanBeHeldWeakly'),
	Canonicalize: require('./2024/Canonicalize'),
	CanonicalNumericIndexString: require('./2024/CanonicalNumericIndexString'),
	CharacterComplement: require('./2024/CharacterComplement'),
	CharacterRange: require('./2024/CharacterRange'),
	clamp: require('./2024/clamp'),
	ClearKeptObjects: require('./2024/ClearKeptObjects'),
	CloneArrayBuffer: require('./2024/CloneArrayBuffer'),
	CodePointAt: require('./2024/CodePointAt'),
	CodePointsToString: require('./2024/CodePointsToString'),
	CompareArrayElements: require('./2024/CompareArrayElements'),
	CompareTypedArrayElements: require('./2024/CompareTypedArrayElements'),
	CompletePropertyDescriptor: require('./2024/CompletePropertyDescriptor'),
	CompletionRecord: require('./2024/CompletionRecord'),
	CopyDataProperties: require('./2024/CopyDataProperties'),
	CreateAsyncFromSyncIterator: require('./2024/CreateAsyncFromSyncIterator'),
	CreateDataProperty: require('./2024/CreateDataProperty'),
	CreateDataPropertyOrThrow: require('./2024/CreateDataPropertyOrThrow'),
	CreateHTML: require('./2024/CreateHTML'),
	CreateIterResultObject: require('./2024/CreateIterResultObject'),
	CreateListFromArrayLike: require('./2024/CreateListFromArrayLike'),
	CreateNonEnumerableDataPropertyOrThrow: require('./2024/CreateNonEnumerableDataPropertyOrThrow'),
	CreateRegExpStringIterator: require('./2024/CreateRegExpStringIterator'),
	DateFromTime: require('./2024/DateFromTime'),
	DateString: require('./2024/DateString'),
	Day: require('./2024/Day'),
	DayFromYear: require('./2024/DayFromYear'),
	DaysInYear: require('./2024/DaysInYear'),
	DayWithinYear: require('./2024/DayWithinYear'),
	DefineMethodProperty: require('./2024/DefineMethodProperty'),
	DefinePropertyOrThrow: require('./2024/DefinePropertyOrThrow'),
	DeletePropertyOrThrow: require('./2024/DeletePropertyOrThrow'),
	DetachArrayBuffer: require('./2024/DetachArrayBuffer'),
	EnumerableOwnProperties: require('./2024/EnumerableOwnProperties'),
	FindViaPredicate: require('./2024/FindViaPredicate'),
	FlattenIntoArray: require('./2024/FlattenIntoArray'),
	floor: require('./2024/floor'),
	FromPropertyDescriptor: require('./2024/FromPropertyDescriptor'),
	Get: require('./2024/Get'),
	GetArrayBufferMaxByteLengthOption: require('./2024/GetArrayBufferMaxByteLengthOption'),
	GetGlobalObject: require('./2024/GetGlobalObject'),
	GetIterator: require('./2024/GetIterator'),
	GetIteratorFromMethod: require('./2024/GetIteratorFromMethod'),
	GetMatchIndexPair: require('./2024/GetMatchIndexPair'),
	GetMatchString: require('./2024/GetMatchString'),
	GetMethod: require('./2024/GetMethod'),
	GetNamedTimeZoneEpochNanoseconds: require('./2024/GetNamedTimeZoneEpochNanoseconds'),
	GetOwnPropertyKeys: require('./2024/GetOwnPropertyKeys'),
	GetPromiseResolve: require('./2024/GetPromiseResolve'),
	GetPrototypeFromConstructor: require('./2024/GetPrototypeFromConstructor'),
	GetStringIndex: require('./2024/GetStringIndex'),
	GetSubstitution: require('./2024/GetSubstitution'),
	GetUTCEpochNanoseconds: require('./2024/GetUTCEpochNanoseconds'),
	GetV: require('./2024/GetV'),
	GetValueFromBuffer: require('./2024/GetValueFromBuffer'),
	GetViewByteLength: require('./2024/GetViewByteLength'),
	GroupBy: require('./2024/GroupBy'),
	HasEitherUnicodeFlag: require('./2024/HasEitherUnicodeFlag'),
	HasOwnProperty: require('./2024/HasOwnProperty'),
	HasProperty: require('./2024/HasProperty'),
	HourFromTime: require('./2024/HourFromTime'),
	InLeapYear: require('./2024/InLeapYear'),
	InstallErrorCause: require('./2024/InstallErrorCause'),
	InstanceofOperator: require('./2024/InstanceofOperator'),
	InternalizeJSONProperty: require('./2024/InternalizeJSONProperty'),
	Invoke: require('./2024/Invoke'),
	IsAccessorDescriptor: require('./2024/IsAccessorDescriptor'),
	IsArray: require('./2024/IsArray'),
	IsArrayBufferViewOutOfBounds: require('./2024/IsArrayBufferViewOutOfBounds'),
	IsBigIntElementType: require('./2024/IsBigIntElementType'),
	IsCallable: require('./2024/IsCallable'),
	IsCompatiblePropertyDescriptor: require('./2024/IsCompatiblePropertyDescriptor'),
	IsConcatSpreadable: require('./2024/IsConcatSpreadable'),
	IsConstructor: require('./2024/IsConstructor'),
	IsDataDescriptor: require('./2024/IsDataDescriptor'),
	IsDetachedBuffer: require('./2024/IsDetachedBuffer'),
	IsExtensible: require('./2024/IsExtensible'),
	IsFixedLengthArrayBuffer: require('./2024/IsFixedLengthArrayBuffer'),
	IsGenericDescriptor: require('./2024/IsGenericDescriptor'),
	IsIntegralNumber: require('./2024/IsIntegralNumber'),
	IsLessThan: require('./2024/IsLessThan'),
	IsLooselyEqual: require('./2024/IsLooselyEqual'),
	IsNoTearConfiguration: require('./2024/IsNoTearConfiguration'),
	IsPromise: require('./2024/IsPromise'),
	IsPropertyKey: require('./2024/IsPropertyKey'),
	IsRegExp: require('./2024/IsRegExp'),
	IsSharedArrayBuffer: require('./2024/IsSharedArrayBuffer'),
	IsStrictlyEqual: require('./2024/IsStrictlyEqual'),
	IsStringWellFormedUnicode: require('./2024/IsStringWellFormedUnicode'),
	IsTimeZoneOffsetString: require('./2024/IsTimeZoneOffsetString'),
	IsTypedArrayOutOfBounds: require('./2024/IsTypedArrayOutOfBounds'),
	IsUnclampedIntegerElementType: require('./2024/IsUnclampedIntegerElementType'),
	IsUnsignedElementType: require('./2024/IsUnsignedElementType'),
	IsValidIntegerIndex: require('./2024/IsValidIntegerIndex'),
	IsViewOutOfBounds: require('./2024/IsViewOutOfBounds'),
	IsWordChar: require('./2024/IsWordChar'),
	IteratorClose: require('./2024/IteratorClose'),
	IteratorComplete: require('./2024/IteratorComplete'),
	IteratorNext: require('./2024/IteratorNext'),
	IteratorStep: require('./2024/IteratorStep'),
	IteratorStepValue: require('./2024/IteratorStepValue'),
	IteratorToList: require('./2024/IteratorToList'),
	IteratorValue: require('./2024/IteratorValue'),
	KeyForSymbol: require('./2024/KeyForSymbol'),
	LengthOfArrayLike: require('./2024/LengthOfArrayLike'),
	MakeDataViewWithBufferWitnessRecord: require('./2024/MakeDataViewWithBufferWitnessRecord'),
	MakeDate: require('./2024/MakeDate'),
	MakeDay: require('./2024/MakeDay'),
	MakeFullYear: require('./2024/MakeFullYear'),
	MakeMatchIndicesIndexPairArray: require('./2024/MakeMatchIndicesIndexPairArray'),
	MakeTime: require('./2024/MakeTime'),
	MakeTypedArrayWithBufferWitnessRecord: require('./2024/MakeTypedArrayWithBufferWitnessRecord'),
	max: require('./2024/max'),
	min: require('./2024/min'),
	MinFromTime: require('./2024/MinFromTime'),
	modulo: require('./2024/modulo'),
	MonthFromTime: require('./2024/MonthFromTime'),
	msFromTime: require('./2024/msFromTime'),
	NewPromiseCapability: require('./2024/NewPromiseCapability'),
	NormalCompletion: require('./2024/NormalCompletion'),
	Number: require('./2024/Number'),
	NumberBitwiseOp: require('./2024/NumberBitwiseOp'),
	NumberToBigInt: require('./2024/NumberToBigInt'),
	NumericToRawBytes: require('./2024/NumericToRawBytes'),
	ObjectDefineProperties: require('./2024/ObjectDefineProperties'),
	OrdinaryCreateFromConstructor: require('./2024/OrdinaryCreateFromConstructor'),
	OrdinaryDefineOwnProperty: require('./2024/OrdinaryDefineOwnProperty'),
	OrdinaryGetOwnProperty: require('./2024/OrdinaryGetOwnProperty'),
	OrdinaryGetPrototypeOf: require('./2024/OrdinaryGetPrototypeOf'),
	OrdinaryHasInstance: require('./2024/OrdinaryHasInstance'),
	OrdinaryHasProperty: require('./2024/OrdinaryHasProperty'),
	OrdinaryObjectCreate: require('./2024/OrdinaryObjectCreate'),
	OrdinarySetPrototypeOf: require('./2024/OrdinarySetPrototypeOf'),
	OrdinaryToPrimitive: require('./2024/OrdinaryToPrimitive'),
	ParseHexOctet: require('./2024/ParseHexOctet'),
	PromiseResolve: require('./2024/PromiseResolve'),
	QuoteJSONString: require('./2024/QuoteJSONString'),
	RawBytesToNumeric: require('./2024/RawBytesToNumeric'),
	RegExpCreate: require('./2024/RegExpCreate'),
	RegExpExec: require('./2024/RegExpExec'),
	RegExpHasFlag: require('./2024/RegExpHasFlag'),
	RequireObjectCoercible: require('./2024/RequireObjectCoercible'),
	SameValue: require('./2024/SameValue'),
	SameValueNonNumber: require('./2024/SameValueNonNumber'),
	SameValueZero: require('./2024/SameValueZero'),
	SecFromTime: require('./2024/SecFromTime'),
	Set: require('./2024/Set'),
	SetFunctionLength: require('./2024/SetFunctionLength'),
	SetFunctionName: require('./2024/SetFunctionName'),
	SetIntegrityLevel: require('./2024/SetIntegrityLevel'),
	SetTypedArrayFromArrayLike: require('./2024/SetTypedArrayFromArrayLike'),
	SetTypedArrayFromTypedArray: require('./2024/SetTypedArrayFromTypedArray'),
	SetValueInBuffer: require('./2024/SetValueInBuffer'),
	SortIndexedProperties: require('./2024/SortIndexedProperties'),
	SpeciesConstructor: require('./2024/SpeciesConstructor'),
	StringCreate: require('./2024/StringCreate'),
	StringGetOwnProperty: require('./2024/StringGetOwnProperty'),
	StringIndexOf: require('./2024/StringIndexOf'),
	StringPad: require('./2024/StringPad'),
	StringPaddingBuiltinsImpl: require('./2024/StringPaddingBuiltinsImpl'),
	StringToBigInt: require('./2024/StringToBigInt'),
	StringToCodePoints: require('./2024/StringToCodePoints'),
	StringToNumber: require('./2024/StringToNumber'),
	substring: require('./2024/substring'),
	SymbolDescriptiveString: require('./2024/SymbolDescriptiveString'),
	SystemTimeZoneIdentifier: require('./2024/SystemTimeZoneIdentifier'),
	TestIntegrityLevel: require('./2024/TestIntegrityLevel'),
	ThisBigIntValue: require('./2024/ThisBigIntValue'),
	ThisBooleanValue: require('./2024/ThisBooleanValue'),
	ThisNumberValue: require('./2024/ThisNumberValue'),
	ThisStringValue: require('./2024/ThisStringValue'),
	ThisSymbolValue: require('./2024/ThisSymbolValue'),
	ThrowCompletion: require('./2024/ThrowCompletion'),
	TimeClip: require('./2024/TimeClip'),
	TimeFromYear: require('./2024/TimeFromYear'),
	TimeString: require('./2024/TimeString'),
	TimeWithinDay: require('./2024/TimeWithinDay'),
	TimeZoneString: require('./2024/TimeZoneString'),
	ToBigInt: require('./2024/ToBigInt'),
	ToBigInt64: require('./2024/ToBigInt64'),
	ToBigUint64: require('./2024/ToBigUint64'),
	ToBoolean: require('./2024/ToBoolean'),
	ToDateString: require('./2024/ToDateString'),
	ToIndex: require('./2024/ToIndex'),
	ToInt16: require('./2024/ToInt16'),
	ToInt32: require('./2024/ToInt32'),
	ToInt8: require('./2024/ToInt8'),
	ToIntegerOrInfinity: require('./2024/ToIntegerOrInfinity'),
	ToLength: require('./2024/ToLength'),
	ToNumber: require('./2024/ToNumber'),
	ToNumeric: require('./2024/ToNumeric'),
	ToObject: require('./2024/ToObject'),
	ToPrimitive: require('./2024/ToPrimitive'),
	ToPropertyDescriptor: require('./2024/ToPropertyDescriptor'),
	ToPropertyKey: require('./2024/ToPropertyKey'),
	ToString: require('./2024/ToString'),
	ToUint16: require('./2024/ToUint16'),
	ToUint32: require('./2024/ToUint32'),
	ToUint8: require('./2024/ToUint8'),
	ToUint8Clamp: require('./2024/ToUint8Clamp'),
	ToZeroPaddedDecimalString: require('./2024/ToZeroPaddedDecimalString'),
	TrimString: require('./2024/TrimString'),
	truncate: require('./2024/truncate'),
	Type: require('./2024/Type'),
	TypedArrayByteLength: require('./2024/TypedArrayByteLength'),
	TypedArrayCreateFromConstructor: require('./2024/TypedArrayCreateFromConstructor'),
	TypedArrayCreateSameType: require('./2024/TypedArrayCreateSameType'),
	TypedArrayElementSize: require('./2024/TypedArrayElementSize'),
	TypedArrayElementType: require('./2024/TypedArrayElementType'),
	TypedArrayGetElement: require('./2024/TypedArrayGetElement'),
	TypedArrayLength: require('./2024/TypedArrayLength'),
	TypedArraySetElement: require('./2024/TypedArraySetElement'),
	TypedArraySpeciesCreate: require('./2024/TypedArraySpeciesCreate'),
	UnicodeEscape: require('./2024/UnicodeEscape'),
	UTF16EncodeCodePoint: require('./2024/UTF16EncodeCodePoint'),
	UTF16SurrogatePairToCodePoint: require('./2024/UTF16SurrogatePairToCodePoint'),
	ValidateAndApplyPropertyDescriptor: require('./2024/ValidateAndApplyPropertyDescriptor'),
	ValidateAtomicAccess: require('./2024/ValidateAtomicAccess'),
	ValidateAtomicAccessOnIntegerTypedArray: require('./2024/ValidateAtomicAccessOnIntegerTypedArray'),
	ValidateIntegerTypedArray: require('./2024/ValidateIntegerTypedArray'),
	ValidateTypedArray: require('./2024/ValidateTypedArray'),
	WeakRefDeref: require('./2024/WeakRefDeref'),
	WeekDay: require('./2024/WeekDay'),
	WordCharacters: require('./2024/WordCharacters'),
	YearFromTime: require('./2024/YearFromTime')
};

module.exports = ES2024;
